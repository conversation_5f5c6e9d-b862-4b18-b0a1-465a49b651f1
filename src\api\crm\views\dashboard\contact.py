"""
Contact Dashboard Views for CRM
Provides analytics endpoints for contact dashboard
"""
from datetime import datetime, timedelta
from django.db.models import Count, Q, Case, When, IntegerField
from django.utils import timezone
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser

from core.models import User as Contact
from api.mixins import AuditMixin, SwaggerTagMixin
from api.utils.cache import DashboardCacheManager
from api.crm.filters.dashboard.contact import CrmDashboardContactFilter
from api.crm.serializers.dashboard.contact import (
    CrmDashboardContactStatsSerializer,
    CrmDashboardContactsByMonthSerializer,
    CrmDashboardNameValueSerializer,
    CrmDashboardCountryContactsSerializer,
)


class CrmDashboardContactViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Contact Dashboard Analytics
    Provides various endpoints for dashboard statistics and charts
    """

    model_class = Contact
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]
    filterset_class = CrmDashboardContactFilter
    swagger_tags = ["Contact Dashboard"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_manager = DashboardCacheManager("crm_contact_dashboard")

    def get_queryset(self):
        """
        Get base queryset for contacts (non-staff users only)
        """
        return Contact.objects.filter(
            deleted=False,
            is_staff=False
        )

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            'search': self.request.GET.get('search', ''),
            'created_at_after': self.request.GET.get('created_at_after', ''),
            'created_at_before': self.request.GET.get('created_at_before', ''),
            'ocupation': self.request.GET.get('ocupation', ''),
            'active': self.request.GET.get('active', ''),
        }

    @action(detail=False, methods=["GET"], url_path="stats")
    def stats(self, request):
        """
        Get general dashboard statistics
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("stats", **cache_params)

        if cached_data:
            return Response(cached_data)

        queryset = self.get_filtered_queryset()

        # Calculate statistics
        total_contacts = queryset.count()

        # New contacts this month
        current_month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_this_month = queryset.filter(created_at__gte=current_month_start).count()

        # Active percentage
        active_contacts = queryset.filter(is_active=True).count()
        active_percentage = (active_contacts / total_contacts * 100) if total_contacts > 0 else 0

        # Conversion rate (placeholder - you might want to define this based on your business logic)
        conversion_rate = 0.0  # This would need to be calculated based on your specific criteria

        # Contacts by status
        contacts_by_status = [
            {"name": "Activos", "value": active_contacts},
            {"name": "Inactivos", "value": total_contacts - active_contacts},
        ]

        data = {
            "total_contacts": total_contacts,
            "new_this_month": new_this_month,
            "active_percentage": round(active_percentage, 2),
            "conversion_rate": conversion_rate,
            "contacts_by_status": contacts_by_status,
        }

        # Cache the result
        self.cache_manager.set("stats", data, timeout=300, **cache_params)

        serializer = CrmDashboardContactStatsSerializer(data)
        return Response(serializer.data)

    @action(detail=False, methods=["GET"], url_path="contacts-by-month")
    def contacts_by_month(self, request):
        """
        Get contacts created by month (last N months)
        """
        months = int(request.GET.get('months', 10))
        cache_params = self.get_cache_key_params()
        cache_params['months'] = months

        cached_data = self.cache_manager.get("contacts_by_month", **cache_params)
        if cached_data:
            return Response(cached_data)

        queryset = self.get_filtered_queryset()

        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=months * 30)  # Approximate

        # Get contacts by month
        month_names = [
            "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
            "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
        ]

        data = []
        total_accumulated = 0

        for i in range(months):
            month_start = (end_date - timedelta(days=(months - i) * 30)).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)

            month_contacts = queryset.filter(
                created_at__gte=month_start,
                created_at__lte=month_end
            ).count()

            total_accumulated += month_contacts

            data.append({
                "name": month_names[month_start.month - 1],
                "contacts": month_contacts,
                "totalAccumulated": total_accumulated,
            })

        # Cache the result
        self.cache_manager.set("contacts_by_month", data, timeout=300, **cache_params)

        serializer = CrmDashboardContactsByMonthSerializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["GET"], url_path="students-by-major")
    def students_by_major(self, request):
        """
        Get distribution of students by major
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("students_by_major", **cache_params)

        if cached_data:
            return Response(cached_data)

        queryset = self.get_filtered_queryset().filter(
            ocupation=Contact.STUDENT_OCUPATION,
            major__isnull=False
        )

        # Group by major and count
        major_counts = queryset.values('major__name').annotate(
            count=Count('uid')
        ).order_by('-count')

        data = [
            {"name": item['major__name'], "value": item['count']}
            for item in major_counts
        ]

        # Cache the result
        self.cache_manager.set("students_by_major", data, timeout=300, **cache_params)

        serializer = CrmDashboardNameValueSerializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["GET"], url_path="students-by-term")
    def students_by_term(self, request):
        """
        Get distribution of students by academic term
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("students_by_term", **cache_params)

        if cached_data:
            return Response(cached_data)

        queryset = self.get_filtered_queryset().filter(
            ocupation=Contact.STUDENT_OCUPATION,
            term__isnull=False
        )

        # Group by term and count
        term_counts = queryset.values('term__name').annotate(
            count=Count('uid')
        ).order_by('-count')

        data = [
            {"name": item['term__name'], "value": item['count']}
            for item in term_counts
        ]

        # Cache the result
        self.cache_manager.set("students_by_term", data, timeout=300, **cache_params)

        serializer = CrmDashboardNameValueSerializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["GET"], url_path="students-by-university")
    def students_by_university(self, request):
        """
        Get distribution of students by university
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("students_by_university", **cache_params)

        if cached_data:
            return Response(cached_data)

        queryset = self.get_filtered_queryset().filter(
            ocupation=Contact.STUDENT_OCUPATION,
            educational_institution__isnull=False
        )

        # Group by educational institution and count
        university_counts = queryset.values('educational_institution__name').annotate(
            count=Count('uid')
        ).order_by('-count')

        data = [
            {"name": item['educational_institution__name'], "value": item['count']}
            for item in university_counts
        ]

        # Cache the result
        self.cache_manager.set("students_by_university", data, timeout=300, **cache_params)

        serializer = CrmDashboardNameValueSerializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["GET"], url_path="employees-by-company")
    def employees_by_company(self, request):
        """
        Get distribution of employees by company
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("employees_by_company", **cache_params)

        if cached_data:
            return Response(cached_data)

        queryset = self.get_filtered_queryset().filter(
            ocupation=Contact.EMPLOYEE_OCUPATION,
            company__isnull=False
        )

        # Group by company and count
        company_counts = queryset.values('company').annotate(
            count=Count('uid')
        ).order_by('-count')

        data = [
            {"name": item['company'], "value": item['count']}
            for item in company_counts
        ]

        # Cache the result
        self.cache_manager.set("employees_by_company", data, timeout=300, **cache_params)

        serializer = CrmDashboardNameValueSerializer(data, many=True)
        return Response(serializer.data)