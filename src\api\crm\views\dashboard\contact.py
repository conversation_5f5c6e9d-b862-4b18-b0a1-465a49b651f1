"""
Contact Dashboard Views for CRM
Provides analytics endpoints for contact dashboard
"""

from datetime import timed<PERSON><PERSON>
from django.db.models import Count
from django.utils import timezone
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, IsAdminUser

from core.models import User as Contact, Order
from api.mixins import AuditMixin, SwaggerTagMixin
from api.utils import CacheManager
from api.crm.filters.dashboard.contact import CrmDashboardContactFilter
from api.crm.serializers.dashboard.contact import CrmDashboardContactsSerializer


class CrmDashboardContactViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.GenericViewSet,
):
    """
    ViewSet for Contact Dashboard Analytics
    Provides various endpoints for dashboard statistics and charts
    """

    model_class = Contact
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated & IsAdminUser]
    filterset_class = CrmDashboardContactFilter
    swagger_tags = ["CRM Dashboard"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_prefix = "crm_dashboard_contact"
        self.cache_manager = CacheManager(self.cache_prefix, timeout=120)

    def initial(self, request, *args, **kwargs):
        """
        Check for force_refresh parameter before any endpoint execution
        """
        super().initial(request, *args, **kwargs)

        # Force refresh cache if requested
        if request.GET.get("force_refresh", "").lower() == "true" and not getattr(
            self, "_cache_invalidated", False
        ):
            self.cache_manager.invalidate()
            self._cache_invalidated = True

    def get_queryset(self):
        """
        Get base queryset for contacts (non-staff users only)
        """
        return Contact.objects.filter(deleted=False, is_staff=False)

    def get_filtered_queryset(self):
        """
        Get filtered queryset based on request filters
        """
        queryset = self.get_queryset()
        filterset = self.filterset_class(self.request.GET, queryset=queryset)
        return filterset.qs if filterset.is_valid() else queryset

    def get_cache_key_params(self):
        """
        Get parameters for cache key generation
        """
        return {
            "search": self.request.GET.get("search", ""),
            "created_at_after": self.request.GET.get("created_at_after", ""),
            "created_at_before": self.request.GET.get("created_at_before", ""),
            "ocupation": self.request.GET.get("ocupation", ""),
            "active": self.request.GET.get("active", ""),
            "country": self.request.GET.get("country", ""),
            "months": self.request.GET.get("months", "10"),
        }

    # === GRANULAR CALCULATION FUNCTIONS ===

    def calculate_general_stats(self, queryset, total_contacts):
        """
        Calculate general dashboard statistics
        """
        # New contacts this month
        current_month_start = timezone.now().replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        new_this_month = queryset.filter(created_at__gte=current_month_start).count()

        # Active percentage
        active_contacts = queryset.filter(is_active=True).count()
        active_percentage = (
            (active_contacts / total_contacts * 100) if total_contacts > 0 else 0
        )

        # Conversion rate (contacts with sold orders)
        contacts_with_sold_orders = (
            queryset.filter(orders__stage=Order.SOLD_STAGE).distinct().count()
        )
        conversion_rate = (
            (contacts_with_sold_orders / total_contacts * 100)
            if total_contacts > 0
            else 0
        )

        # Contacts by status
        contacts_by_status = [
            {"name": "Activos", "value": active_contacts},
            {"name": "Inactivos", "value": total_contacts - active_contacts},
        ]

        return {
            "total_contacts": total_contacts,
            "new_this_month": new_this_month,
            "active_percentage": round(active_percentage, 2),
            "conversion_rate": round(conversion_rate, 2),
            "contacts_by_status": contacts_by_status,
        }

    def calculate_contacts_by_month(self, queryset, months=10):
        """
        Calculate contacts created by month
        """
        end_date = timezone.now()
        month_names = [
            "Enero",
            "Febrero",
            "Marzo",
            "Abril",
            "Mayo",
            "Junio",
            "Julio",
            "Agosto",
            "Septiembre",
            "Octubre",
            "Noviembre",
            "Diciembre",
        ]

        data = []
        total_accumulated = 0

        for i in range(months):
            month_start = (end_date - timedelta(days=(months - i) * 30)).replace(
                day=1, hour=0, minute=0, second=0, microsecond=0
            )
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(
                seconds=1
            )

            month_contacts = queryset.filter(
                created_at__gte=month_start, created_at__lte=month_end
            ).count()

            total_accumulated += month_contacts

            data.append(
                {
                    "name": month_names[month_start.month - 1],
                    "contacts": month_contacts,
                    "totalAccumulated": total_accumulated,
                }
            )

        return data

    def calculate_contacts_by_country(self, queryset):
        """
        Calculate distribution of contacts by country
        """
        country_counts = (
            queryset.filter(country__isnull=False)
            .values("country")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"country": item["country"], "contacts": item["count"]}
            for item in country_counts
        ]

    def calculate_contacts_by_occupation(self, queryset):
        """
        Calculate distribution of contacts by occupation
        """
        occupation_display = {
            Contact.STUDENT_OCUPATION: "Estudiantes",
            Contact.EMPLOYEE_OCUPATION: "Empleados",
            Contact.INDEPENDENT_OCUPATION: "Independientes",
        }

        occupation_counts = (
            queryset.values("ocupation").annotate(count=Count("uid")).order_by("-count")
        )

        data = []
        for item in occupation_counts:
            if item["ocupation"]:
                display_name = occupation_display.get(
                    item["ocupation"], item["ocupation"]
                )
                data.append({"name": display_name, "value": item["count"]})

        return data

    def calculate_students_by_major(self, queryset):
        """
        Calculate distribution of students by major
        """
        student_queryset = queryset.filter(
            ocupation=Contact.STUDENT_OCUPATION, major__isnull=False
        )

        major_counts = (
            student_queryset.values("major__name")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["major__name"], "value": item["count"]}
            for item in major_counts
        ]

    def calculate_students_by_term(self, queryset):
        """
        Calculate distribution of students by academic term
        """
        student_queryset = queryset.filter(
            ocupation=Contact.STUDENT_OCUPATION, term__isnull=False
        )

        term_counts = (
            student_queryset.values("term__name")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["term__name"], "value": item["count"]} for item in term_counts
        ]

    def calculate_students_by_university(self, queryset):
        """
        Calculate distribution of students by university
        """
        student_queryset = queryset.filter(
            ocupation=Contact.STUDENT_OCUPATION, educational_institution__isnull=False
        )

        university_counts = (
            student_queryset.values("educational_institution__name")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["educational_institution__name"], "value": item["count"]}
            for item in university_counts
        ]

    def calculate_employees_by_company(self, queryset):
        """
        Calculate distribution of employees by company
        """
        employee_queryset = queryset.filter(
            ocupation=Contact.EMPLOYEE_OCUPATION, company__isnull=False
        )

        company_counts = (
            employee_queryset.values("company")
            .annotate(count=Count("uid"))
            .order_by("-count")
        )

        return [
            {"name": item["company"], "value": item["count"]} for item in company_counts
        ]

    # === CONSOLIDATED ENDPOINT ===

    def list(self, request):
        """
        Get all dashboard data in a single consolidated response
        Optimized for frontend dashboard loading with single cache entry
        """
        cache_params = self.get_cache_key_params()
        cached_data = self.cache_manager.get("list", **cache_params)
        info = self.cache_manager.debug_cache_info("list", **cache_params)
        print(f"🔄 Cache info: {info}")

        if cached_data:
            return Response(cached_data)

        # Single filtered queryset for all calculations
        queryset = self.get_filtered_queryset()
        total_contacts = queryset.count()
        months = int(request.GET.get("months", 10))

        # Calculate all data using granular functions
        data = {
            "stats": self.calculate_general_stats(queryset, total_contacts),
            "contacts_by_month": self.calculate_contacts_by_month(queryset, months),
            "contacts_by_country": self.calculate_contacts_by_country(queryset),
            "contacts_by_occupation": self.calculate_contacts_by_occupation(queryset),
            "students_by_major": self.calculate_students_by_major(queryset),
            "students_by_term": self.calculate_students_by_term(queryset),
            "students_by_university": self.calculate_students_by_university(queryset),
            "employees_by_company": self.calculate_employees_by_company(queryset),
        }

        # Cache the consolidated result
        self.cache_manager.set("list", data, **cache_params)

        return Response(CrmDashboardContactsSerializer(data).data)
