"""
Cache utilities for the API
"""
import hashlib
from functools import wraps
from django.core.cache import cache
from django.conf import settings


def generate_cache_key(prefix, *args, **kwargs):
    """
    Generate a cache key based on prefix and arguments
    """
    key_parts = [prefix]
    
    # Add positional arguments
    for arg in args:
        key_parts.append(str(arg))
    
    # Add keyword arguments (sorted for consistency)
    for key, value in sorted(kwargs.items()):
        key_parts.append(f"{key}:{value}")
    
    # Create the key
    key = ":".join(key_parts)
    
    # If key is too long, hash it
    if len(key) > 200:
        key_hash = hashlib.md5(key.encode()).hexdigest()
        key = f"{prefix}:hash:{key_hash}"
    
    return key


def cache_dashboard_data(timeout=300, key_prefix="dashboard"):
    """
    Decorator to cache dashboard data
    
    Args:
        timeout: Cache timeout in seconds (default: 5 minutes)
        key_prefix: Prefix for cache key
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key based on function name and arguments
            cache_key = generate_cache_key(
                key_prefix,
                func.__name__,
                *args[1:],  # Skip 'self' argument
                **kwargs
            )
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


def invalidate_dashboard_cache(key_prefix="dashboard"):
    """
    Invalidate all dashboard cache keys with given prefix
    """
    # This is a simple implementation
    # In production, you might want to use cache versioning or tags
    pass


class DashboardCacheManager:
    """
    Manager for dashboard cache operations
    """
    
    def __init__(self, prefix="crm_dashboard"):
        self.prefix = prefix
    
    def get_cache_key(self, endpoint, **filters):
        """
        Generate cache key for dashboard endpoint
        """
        return generate_cache_key(self.prefix, endpoint, **filters)
    
    def get(self, endpoint, **filters):
        """
        Get cached data for dashboard endpoint
        """
        cache_key = self.get_cache_key(endpoint, **filters)
        return cache.get(cache_key)
    
    def set(self, endpoint, data, timeout=300, **filters):
        """
        Set cached data for dashboard endpoint
        """
        cache_key = self.get_cache_key(endpoint, **filters)
        cache.set(cache_key, data, timeout)
    
    def invalidate(self, endpoint=None, **filters):
        """
        Invalidate cache for specific endpoint or all dashboard cache
        """
        if endpoint:
            cache_key = self.get_cache_key(endpoint, **filters)
            cache.delete(cache_key)
        else:
            # For now, we'll implement a simple approach
            # In production, consider using cache versioning
            pass
