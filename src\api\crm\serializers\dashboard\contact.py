"""
Contact dashboard generic serializer
"""
from rest_framework import serializers


class CrmDashboardContactStatsSerializer(serializers.Serializer):
    """
    Serializer for contact dashboard statistics
    """
    total_contacts = serializers.IntegerField()
    new_this_month = serializers.IntegerField()
    active_percentage = serializers.FloatField()
    conversion_rate = serializers.FloatField()
    contacts_by_status = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField()
        )
    )


class CrmDashboardContactsByMonthSerializer(serializers.Serializer):
    """
    Serializer for contacts by month data
    """
    name = serializers.CharField()
    contacts = serializers.IntegerField()
    totalAccumulated = serializers.IntegerField()


class CrmDashboardNameValueSerializer(serializers.Serializer):
    """
    Generic serializer for name-value pairs used in charts
    """
    name = serializers.CharField()
    value = serializers.IntegerField()


class CrmDashboardCountryContactsSerializer(serializers.Serializer):
    """
    Serializer for contacts by country (TreeMap data)
    """
    country = serializers.CharField()
    contacts = serializers.IntegerField()