from django_filters import rest_framework as filters
from core.models import User as Contact
from django.db.models import Q


class CrmDashboardContactFilter(filters.FilterSet):
    """
    Filter for the Contact Dashboard.
    Allows filtering contacts for dashboard analytics.
    """

    # Search by name, email or phone
    search = filters.CharFilter(
        method="filter_search",
        label="Search by name, email or phone"
    )

    # Date range filter for registration date
    created_at = filters.DateFromToRangeFilter()

    # Multiple choice filter for occupation
    ocupation = filters.MultipleChoiceFilter(
        choices=Contact.OCUPATION_CHOICES,
        field_name="ocupation",
        conjoined=False,  # OR logic for multiple values
    )

    # Boolean filter for active status
    active = filters.BooleanFilter(field_name="is_active")

    def filter_search(self, queryset, _, value):
        """
        Filter contacts by name, email or phone number
        """
        if not value:
            return queryset

        return queryset.filter(
            Q(first_name__icontains=value) |
            Q(last_name__icontains=value) |
            Q(email__icontains=value) |
            Q(phone_number__icontains=value)
        )

    class Meta:
        model = Contact
        fields = [
            "search",
            "created_at",
            "ocupation",
            "active",
        ]